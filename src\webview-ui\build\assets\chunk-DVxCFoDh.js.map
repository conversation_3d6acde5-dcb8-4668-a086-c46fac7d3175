{"version": 3, "file": "chunk-DVxCFoDh.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.2/node_modules/@shikijs/langs/dist/sdbl.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"1C (Query)\\\",\\\"fileTypes\\\":[\\\"sdbl\\\",\\\"query\\\"],\\\"firstLineMatch\\\":\\\"(?i)Выбрать|Select(\\\\\\\\s+Разрешенные|\\\\\\\\s+Allowed)?(\\\\\\\\s+Различные|\\\\\\\\s+Distinct)?(\\\\\\\\s+Первые|\\\\\\\\s+Top)?.*\\\",\\\"name\\\":\\\"sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"^(\\\\\\\\s*//.*)$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"(?!\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sdbl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.character.escape.sdbl\\\"},{\\\"match\\\":\\\"^(\\\\\\\\s*//.*)$\\\",\\\"name\\\":\\\"comment.line.double-slash.sdbl\\\"}]},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"constant.language.sdbl\\\"},{\\\"match\\\":\\\"(?<=[^.а-яё\\\\\\\\w]|^)(\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"constant.numeric.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"keyword.control.conditional.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<!КАК\\\\\\\\s|AS\\\\\\\\s)(?<=[^.а-яё\\\\\\\\w]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\\\\\s+ИЕРАРХИИ|IN\\\\\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\\\\\s+НЕ)?\\\\\\\\s+NULL|Is(\\\\\\\\s+NOT)?\\\\\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"keyword.operator.logical.sdbl\\\"},{\\\"match\\\":\\\"<=|>=|[<=>]\\\",\\\"name\\\":\\\"keyword.operator.comparison.sdbl\\\"},{\\\"match\\\":\\\"([-%*+/])\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.sdbl\\\"},{\\\"match\\\":\\\"([,;])\\\",\\\"name\\\":\\\"keyword.operator.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\\\\\s+(Внешнее\\\\\\\\s+|Outer\\\\\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\\\\\s+По(\\\\\\\\s+Группирующим\\\\\\\\s+Наборам)?)|(Group\\\\\\\\s+By(\\\\\\\\s+Grouping\\\\\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\\\\\s+Все)?|Union(\\\\\\\\s+All)?|(Упорядочить\\\\\\\\s+По)|(Order\\\\\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\\\\\s+Общие)?|By(\\\\\\\\s+Overall)?|(Только\\\\\\\\s+)?Иерархия|(Only\\\\\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\\\\\s+Изменения|(For\\\\\\\\s+Update(\\\\\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"keyword.control.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.sdbl\\\"},{\\\"match\\\":\\\"(?i)(?<=[^.а-яё\\\\\\\\w])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^.а-яё\\\\\\\\w]|$)\\\",\\\"name\\\":\\\"support.type.sdbl\\\"},{\\\"match\\\":\\\"(&[а-яё\\\\\\\\w]+)\\\",\\\"name\\\":\\\"variable.parameter.sdbl\\\"}],\\\"scopeName\\\":\\\"source.sdbl\\\",\\\"aliases\\\":[\\\"1c-query\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": ["lang", "sdbl"], "mappings": "AAAA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,mrHAAs4H,CAAC,EAE95HC,EAAA,CACfD,CACA", "x_google_ignoreList": [0]}