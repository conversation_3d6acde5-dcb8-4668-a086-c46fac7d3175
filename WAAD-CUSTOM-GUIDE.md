# 🎯 دليل استخدام Waad Code المخصص

## 🚀 مرحباً بك في Waad Code!

تم إنشاء **Waad Code** خصيصاً لك مع مقدم خدمة ذكاء اصطناعي مخصص يحتوي على 4 نماذج متخصصة.

## 📦 التثبيت والإعداد

### 1. تثبيت الإضافة
```bash
# تثبيت الإضافة في VS Code
code --install-extension bin/waad-code-1.0.0.vsix

# أو يدوياً:
# 1. افتح VS Code
# 2. اذهب إلى Extensions (Ctrl+Shift+X)
# 3. اضغط على "..." واختر "Install from VSIX"
# 4. اختر ملف waad-code-1.0.0.vsix
```

### 2. إعداد المقدم المخصص
1. افتح VS Code
2. اضغط `Ctrl+Shift+A` لفتح Waad Code
3. اذهب إلى الإعدادات ⚙️
4. اختر **"waad-custom"** كمقدم الخدمة
5. اختر النموذج المناسب

## 🤖 النماذج المتاحة

### 1. 🤖 النموذج الأساسي (`waad-custom-model`)
**الاستخدام**: للمحادثات العامة والمساعدة الأساسية

**مثال على الاستخدام**:
```
المستخدم: مرحباً، كيف يمكنك مساعدتي؟
النموذج: مرحباً! أنا مساعد الذكاء الاصطناعي المخصص لـ Waad. يمكنني مساعدتك في:
🔧 البرمجة والتطوير
📊 قواعد البيانات  
🌐 تطوير الويب
💡 أفكار وحلول
```

### 2. 💻 مساعد البرمجة (`waad-coding-assistant`)
**الاستخدام**: كتابة وتحسين الكود

**مثال على الاستخدام**:
```
المستخدم: اكتب لي دالة JavaScript لحساب المتوسط
النموذج: بالطبع! إليك دالة لحساب المتوسط:

```javascript
function calculateAverage(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) {
        return 0;
    }
    
    const sum = numbers.reduce((acc, num) => acc + num, 0);
    return sum / numbers.length;
}

// مثال على الاستخدام
const grades = [85, 92, 78, 96, 88];
console.log(calculateAverage(grades)); // 87.8
```

هل تريد مني إضافة المزيد من الميزات؟
```

### 3. 🗄️ خبير قواعد البيانات (`waad-database-expert`)
**الاستخدام**: تصميم قواعد البيانات وكتابة SQL

**مثال على الاستخدام**:
```
المستخدم: صمم لي جدول للمستخدمين
النموذج: إليك تصميم جدول المستخدمين لـ SQL Server:

```sql
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(50),
    LastName NVARCHAR(50),
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1
);

-- إضافة فهارس للأداء
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_Username ON Users(Username);
```

هل تريد إضافة جداول أخرى مرتبطة؟
```

### 4. 🌐 مطور الويب (`waad-web-developer`)
**الاستخدام**: تطوير تطبيقات الويب الحديثة

**مثال على الاستخدام**:
```
المستخدم: اعمل لي مكون React للتسجيل
النموذج: إليك مكون تسجيل الدخول باستخدام React:

```jsx
import React, { useState } from 'react';

const LoginForm = () => {
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            // منطق تسجيل الدخول هنا
            console.log('تسجيل الدخول:', formData);
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="login-form">
            <h2>تسجيل الدخول</h2>
            
            <input
                type="email"
                placeholder="البريد الإلكتروني"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                required
            />
            
            <input
                type="password"
                placeholder="كلمة المرور"
                value={formData.password}
                onChange={(e) => setFormData({...formData, password: e.target.value})}
                required
            />
            
            <button type="submit" disabled={loading}>
                {loading ? 'جاري التسجيل...' : 'تسجيل الدخول'}
            </button>
        </form>
    );
};

export default LoginForm;
```

هل تريد إضافة التحقق من صحة البيانات؟
```

## ⚙️ الإعدادات المتقدمة

### تخصيص رسالة النظام
يمكنك إضافة رسالة نظام مخصصة لتوجيه سلوك النموذج:

```
أنت مساعد برمجة متخصص في تطوير تطبيقات الويب باستخدام React و Node.js. 
اكتب كود نظيف ومعلق باللغة العربية. 
استخدم أفضل الممارسات في البرمجة.
```

### ضبط مستوى الإبداع
- **منخفض (0.1)**: إجابات دقيقة ومحددة
- **متوسط (0.5)**: توازن بين الدقة والإبداع  
- **عالي (0.7)**: إجابات إبداعية ومتنوعة
- **أقصى (1.0)**: أقصى مستوى إبداع

## 🔧 استكشاف الأخطاء

### المشكلة: النموذج لا يظهر في القائمة
**الحل**: 
1. تأكد من تثبيت الإضافة بشكل صحيح
2. أعد تشغيل VS Code
3. تحقق من أن المقدم مضبوط على "waad-custom"

### المشكلة: النموذج لا يجيب
**الحل**:
1. تحقق من اتصال الإنترنت
2. تأكد من صحة إعدادات API
3. جرب نموذج آخر

## 🚀 الخطوات التالية

### 1. تطوير المزيد من النماذج
يمكنك إضافة نماذج جديدة في ملف:
```
src/api/providers/waad-custom.ts
```

### 2. تخصيص الواجهة
عدل واجهة المستخدم في:
```
webview-ui/src/components/waad/WaadCustomSettings.tsx
```

### 3. إضافة ميزات جديدة
- دعم المزيد من اللغات
- تكامل مع APIs خارجية
- حفظ المحادثات
- تصدير الكود

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:

1. **راجع الملفات**:
   - `setup-local-db.md` - إعداد قاعدة البيانات
   - `cloud-deployment-guide.md` - النشر السحابي
   - `DEVELOPMENT.md` - دليل التطوير

2. **تحقق من الأخطاء**:
   - افتح Developer Tools في VS Code
   - راجع Output panel
   - تحقق من ملفات الـ logs

3. **تطوير مخصص**:
   - عدل الكود حسب احتياجاتك
   - أضف نماذج جديدة
   - شارك تحسيناتك

---

## 🎉 مبروك!

لديك الآن مساعد ذكاء اصطناعي مخصص بالكامل! استمتع بالبرمجة مع **Waad Code** 🚀
