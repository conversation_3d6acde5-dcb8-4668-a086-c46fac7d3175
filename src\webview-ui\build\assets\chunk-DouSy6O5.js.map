{"version": 3, "file": "chunk-DouSy6O5.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.2/node_modules/@shikijs/langs/dist/erb.mjs"], "sourcesContent": ["import html from './html.mjs'\nimport ruby from './ruby.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"ERB\\\",\\\"fileTypes\\\":[\\\"erb\\\",\\\"rhtml\\\",\\\"html.erb\\\"],\\\"injections\\\":{\\\"text.html.erb - (meta.embedded.block.erb | meta.embedded.line.erb | comment)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?=<%+#(?![^%]*%>))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.erb\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)(\\\\\\\\s*$\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.trailing.erb\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"}]},{\\\"begin\\\":\\\"^(\\\\\\\\s*)(?=<%(?![^%]*%>))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.whitespace.embedded.leading.erb\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)(\\\\\\\\s*$\\\\\\\\n)?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.whitespace.embedded.trailing.erb\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#tags\\\"}]},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#tags\\\"}]}},\\\"name\\\":\\\"erb\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"text.html.basic\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"<%+#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.erb\\\"}},\\\"end\\\":\\\"%>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.erb\\\"}},\\\"name\\\":\\\"comment.block.erb\\\"}]},\\\"tags\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"<%+(?!>)[-=]?(?![^%]*%>)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.erb\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"(-?%)>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.erb\\\"},\\\"1\\\":{\\\"name\\\":\\\"source.ruby\\\"}},\\\"name\\\":\\\"meta.embedded.block.erb\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.erb\\\"}},\\\"match\\\":\\\"(#).*?(?=-?%>)\\\",\\\"name\\\":\\\"comment.line.number-sign.erb\\\"},{\\\"include\\\":\\\"source.ruby\\\"}]},{\\\"begin\\\":\\\"<%+(?!>)[-=]?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.erb\\\"}},\\\"contentName\\\":\\\"source.ruby\\\",\\\"end\\\":\\\"(-?%)>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.erb\\\"},\\\"1\\\":{\\\"name\\\":\\\"source.ruby\\\"}},\\\"name\\\":\\\"meta.embedded.line.erb\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.erb\\\"}},\\\"match\\\":\\\"(#).*?(?=-?%>)\\\",\\\"name\\\":\\\"comment.line.number-sign.erb\\\"},{\\\"include\\\":\\\"source.ruby\\\"}]}]}},\\\"scopeName\\\":\\\"text.html.erb\\\",\\\"embeddedLangs\\\":[\\\"html\\\",\\\"ruby\\\"]}\"))\n\nexport default [\n...html,\n...ruby,\nlang\n]\n"], "names": ["lang", "erb", "html", "ruby"], "mappings": "kiBAGA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,w8DAAgtE,CAAC,EAExuEC,EAAA,CACf,GAAGC,EACH,GAAGC,EACHH,CACA", "x_google_ignoreList": [0]}