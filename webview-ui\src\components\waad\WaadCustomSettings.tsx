import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { Textarea } from '../ui/textarea'
import { Input } from '../ui/input'
import { Label } from '../ui/label'

/**
 * إعدادات المقدم المخصص لـ Waad
 * Waad Custom Provider Settings
 */

interface WaadCustomModel {
	id: string
	name: string
	description: string
	icon: string
	specialties: string[]
}

const WAAD_MODELS: WaadCustomModel[] = [
	{
		id: "waad-custom-model",
		name: "النموذج الأساسي",
		description: "نموذج عام للاستخدامات المتنوعة",
		icon: "🤖",
		specialties: ["محادثة عامة", "مساعدة أساسية", "أسئلة وأجوبة"]
	},
	{
		id: "waad-coding-assistant",
		name: "مساعد البرمجة",
		description: "متخصص في كتابة وتحسين الكود",
		icon: "💻",
		specialties: ["JavaScript", "TypeScript", "React", "Node.js", "Python", "إصلاح الأخطاء"]
	},
	{
		id: "waad-database-expert",
		name: "خبير قواعد البيانات",
		description: "متخصص في قواعد البيانات والـ SQL",
		icon: "🗄️",
		specialties: ["SQL Server", "PostgreSQL", "تصميم قواعد البيانات", "تحسين الاستعلامات"]
	},
	{
		id: "waad-web-developer",
		name: "مطور الويب",
		description: "متخصص في تطوير تطبيقات الويب الحديثة",
		icon: "🌐",
		specialties: ["React", "Vue.js", "CSS", "HTML", "REST APIs", "GraphQL"]
	}
]

interface WaadCustomSettingsProps {
	selectedModel?: string
	onModelChange?: (modelId: string) => void
	customPrompt?: string
	onCustomPromptChange?: (prompt: string) => void
}

export const WaadCustomSettings: React.FC<WaadCustomSettingsProps> = ({
	selectedModel = "waad-custom-model",
	onModelChange,
	customPrompt = "",
	onCustomPromptChange
}) => {
	const [activeModel, setActiveModel] = useState(selectedModel)
	const [prompt, setPrompt] = useState(customPrompt)

	const handleModelSelect = (modelId: string) => {
		setActiveModel(modelId)
		onModelChange?.(modelId)
	}

	const handlePromptChange = (newPrompt: string) => {
		setPrompt(newPrompt)
		onCustomPromptChange?.(newPrompt)
	}

	const selectedModelData = WAAD_MODELS.find(m => m.id === activeModel)

	return (
		<div className="space-y-6">
			{/* عنوان الإعدادات */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						🎯 إعدادات Waad المخصصة
					</CardTitle>
					<CardDescription>
						اختر النموذج المناسب لاحتياجاتك وخصص الإعدادات
					</CardDescription>
				</CardHeader>
			</Card>

			{/* اختيار النموذج */}
			<Card>
				<CardHeader>
					<CardTitle>اختيار النموذج</CardTitle>
					<CardDescription>
						اختر النموذج المتخصص الذي يناسب مهمتك
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{WAAD_MODELS.map((model) => (
							<Card 
								key={model.id}
								className={`cursor-pointer transition-all hover:shadow-md ${
									activeModel === model.id 
										? 'ring-2 ring-blue-500 bg-blue-50' 
										: 'hover:bg-gray-50'
								}`}
								onClick={() => handleModelSelect(model.id)}
							>
								<CardContent className="p-4">
									<div className="flex items-start gap-3">
										<span className="text-2xl">{model.icon}</span>
										<div className="flex-1">
											<h3 className="font-semibold text-sm">{model.name}</h3>
											<p className="text-xs text-gray-600 mt-1">{model.description}</p>
											<div className="flex flex-wrap gap-1 mt-2">
												{model.specialties.slice(0, 3).map((specialty) => (
													<Badge key={specialty} variant="secondary" className="text-xs">
														{specialty}
													</Badge>
												))}
												{model.specialties.length > 3 && (
													<Badge variant="outline" className="text-xs">
														+{model.specialties.length - 3}
													</Badge>
												)}
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</CardContent>
			</Card>

			{/* تفاصيل النموذج المختار */}
			{selectedModelData && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<span>{selectedModelData.icon}</span>
							{selectedModelData.name}
						</CardTitle>
						<CardDescription>{selectedModelData.description}</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div>
								<Label className="text-sm font-medium">التخصصات:</Label>
								<div className="flex flex-wrap gap-2 mt-2">
									{selectedModelData.specialties.map((specialty) => (
										<Badge key={specialty} variant="secondary">
											{specialty}
										</Badge>
									))}
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* إعدادات مخصصة */}
			<Card>
				<CardHeader>
					<CardTitle>إعدادات مخصصة</CardTitle>
					<CardDescription>
						خصص سلوك النموذج حسب احتياجاتك
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div>
						<Label htmlFor="custom-prompt">رسالة النظام المخصصة (اختياري)</Label>
						<Textarea
							id="custom-prompt"
							placeholder="أدخل تعليمات مخصصة للنموذج..."
							value={prompt}
							onChange={(e) => handlePromptChange(e.target.value)}
							className="mt-2"
							rows={4}
						/>
						<p className="text-xs text-gray-500 mt-1">
							هذه الرسالة ستؤثر على سلوك النموذج وطريقة إجابته
						</p>
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div>
							<Label htmlFor="temperature">مستوى الإبداع</Label>
							<Select defaultValue="0.7">
								<SelectTrigger>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="0.1">منخفض (دقيق)</SelectItem>
									<SelectItem value="0.5">متوسط</SelectItem>
									<SelectItem value="0.7">عالي (إبداعي)</SelectItem>
									<SelectItem value="1.0">أقصى إبداع</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div>
							<Label htmlFor="max-tokens">الحد الأقصى للكلمات</Label>
							<Input
								id="max-tokens"
								type="number"
								defaultValue="8192"
								min="100"
								max="32000"
							/>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* أزرار الإجراءات */}
			<div className="flex gap-2">
				<Button className="flex-1">
					💾 حفظ الإعدادات
				</Button>
				<Button variant="outline">
					🔄 إعادة تعيين
				</Button>
				<Button variant="outline">
					🧪 اختبار النموذج
				</Button>
			</div>
		</div>
	)
}

export default WaadCustomSettings
