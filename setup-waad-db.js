#!/usr/bin/env node

/**
 * سكريبت إعداد قاعدة البيانات المحلية لـ Waad Code
 * Local Database Setup Script for Waad Code
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء إعداد قاعدة البيانات المحلية لـ Waad Code...');
console.log('🚀 Starting Waad Code local database setup...\n');

// التحقق من وجود PostgreSQL
function checkPostgreSQL() {
    try {
        execSync('psql --version', { stdio: 'pipe' });
        console.log('✅ PostgreSQL مثبت');
        return true;
    } catch (error) {
        console.log('❌ PostgreSQL غير مثبت');
        console.log('📋 يرجى تثبيت PostgreSQL من: https://www.postgresql.org/download/');
        return false;
    }
}

// التحقق من وجود Redis
function checkRedis() {
    try {
        execSync('redis-cli --version', { stdio: 'pipe' });
        console.log('✅ Redis مثبت');
        return true;
    } catch (error) {
        console.log('❌ Redis غير مثبت');
        console.log('📋 يرجى تثبيت Redis');
        return false;
    }
}

// إنشاء قواعد البيانات
function createDatabases() {
    console.log('\n📊 إنشاء قواعد البيانات...');
    
    try {
        // إنشاء قاعدة بيانات التطوير
        execSync('psql -U postgres -c "CREATE DATABASE IF NOT EXISTS evals_development;"', { stdio: 'pipe' });
        console.log('✅ تم إنشاء evals_development');
        
        // إنشاء قاعدة بيانات الاختبار
        execSync('psql -U postgres -c "CREATE DATABASE IF NOT EXISTS evals_test;"', { stdio: 'pipe' });
        console.log('✅ تم إنشاء evals_test');
        
        return true;
    } catch (error) {
        console.log('❌ فشل في إنشاء قواعد البيانات');
        console.log('💡 تأكد من أن PostgreSQL يعمل وأن كلمة المرور صحيحة');
        return false;
    }
}

// اختبار الاتصال بقاعدة البيانات
function testDatabaseConnection() {
    console.log('\n🔍 اختبار الاتصال بقاعدة البيانات...');
    
    try {
        execSync('psql -U postgres -d evals_development -c "SELECT version();"', { stdio: 'pipe' });
        console.log('✅ الاتصال بـ PostgreSQL ناجح');
        return true;
    } catch (error) {
        console.log('❌ فشل الاتصال بـ PostgreSQL');
        return false;
    }
}

// اختبار الاتصال بـ Redis
function testRedisConnection() {
    console.log('\n🔍 اختبار الاتصال بـ Redis...');
    
    try {
        const result = execSync('redis-cli ping', { encoding: 'utf8' });
        if (result.trim() === 'PONG') {
            console.log('✅ الاتصال بـ Redis ناجح');
            return true;
        }
    } catch (error) {
        console.log('❌ فشل الاتصال بـ Redis');
        console.log('💡 تأكد من أن Redis يعمل: redis-server');
        return false;
    }
}

// تشغيل المايجريشن
function runMigrations() {
    console.log('\n🔄 تشغيل مايجريشن قاعدة البيانات...');
    
    try {
        process.chdir('packages/evals');
        execSync('pnpm run db:push', { stdio: 'inherit' });
        console.log('✅ تم تشغيل المايجريشن بنجاح');
        process.chdir('../..');
        return true;
    } catch (error) {
        console.log('❌ فشل في تشغيل المايجريشن');
        process.chdir('../..');
        return false;
    }
}

// الدالة الرئيسية
async function main() {
    const postgresOK = checkPostgreSQL();
    const redisOK = checkRedis();
    
    if (!postgresOK || !redisOK) {
        console.log('\n❌ يرجى تثبيت المتطلبات المفقودة أولاً');
        console.log('📖 راجع ملف setup-local-db.md للتعليمات التفصيلية');
        process.exit(1);
    }
    
    const dbCreated = createDatabases();
    if (!dbCreated) {
        process.exit(1);
    }
    
    const dbConnected = testDatabaseConnection();
    const redisConnected = testRedisConnection();
    
    if (!dbConnected || !redisConnected) {
        process.exit(1);
    }
    
    const migrationsOK = runMigrations();
    if (!migrationsOK) {
        process.exit(1);
    }
    
    console.log('\n🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('🎉 Database setup completed successfully!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. أضف مفاتيح API في ملف .env.local');
    console.log('2. شغل المشروع: pnpm run bundle && code --install-extension bin/waad-code-1.0.0.vsix');
}

main().catch(console.error);
