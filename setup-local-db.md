# إعداد قاعدة البيانات المحلية لـ Waad Code

## 1. تثبيت PostgreSQL

### Windows:
1. حمل PostgreSQL من: https://www.postgresql.org/download/windows/
2. اختر الإصدار 15 أو أحدث
3. أثناء التثبيت:
   - Username: `postgres`
   - Password: `password` (أو كلمة مرور من اختيارك)
   - Port: `5432`

### macOS:
```bash
brew install postgresql@15
brew services start postgresql@15
```

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 2. تثبيت Redis

### Windows:
1. حمل Redis من: https://github.com/microsoftarchive/redis/releases
2. أو استخدم WSL:
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

### macOS:
```bash
brew install redis
brew services start redis
```

### Linux:
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 3. إنشاء قواعد البيانات

بعد تثبيت PostgreSQL، افتح terminal وقم بتشغيل:

```bash
# الاتصال بـ PostgreSQL
psql -U postgres

# إنشاء قواعد البيانات
CREATE DATABASE evals_development;
CREATE DATABASE evals_test;

# الخروج
\q
```

## 4. إعداد متغيرات البيئة

سيتم إنشاء ملف `.env.local` تلقائياً مع الإعدادات المطلوبة.

## 5. تشغيل المايجريشن

بعد إعداد قواعد البيانات، سيتم تشغيل المايجريشن تلقائياً.

## 6. التحقق من الاتصال

```bash
# التحقق من PostgreSQL
psql -U postgres -d evals_development -c "SELECT version();"

# التحقق من Redis
redis-cli ping
```

يجب أن ترى:
- PostgreSQL: إصدار قاعدة البيانات
- Redis: `PONG`
