{"version": 3, "file": "chunk-D9kx8fwg.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.2/node_modules/@shikijs/langs/dist/shellsession.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Shell Session\\\",\\\"fileTypes\\\":[\\\"sh-session\\\"],\\\"name\\\":\\\"shellsession\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.prompt-prefix.shell-session\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.prompt.shell-session\\\"},\\\"3\\\":{\\\"name\\\":\\\"source.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]}},\\\"match\\\":\\\"^(?:((?:\\\\\\\\(\\\\\\\\S+\\\\\\\\)\\\\\\\\s*)?(?:sh\\\\\\\\S*?|\\\\\\\\w+\\\\\\\\S+[:@]\\\\\\\\S+(?:\\\\\\\\s+\\\\\\\\S+)?|\\\\\\\\[\\\\\\\\S+?[:@]\\\\\\\\N+?].*?))\\\\\\\\s*)?([#$%>❯➜\\\\\\\\p{Greek}])\\\\\\\\s+(.*)$\\\"},{\\\"match\\\":\\\"^.+$\\\",\\\"name\\\":\\\"meta.output.shell-session\\\"}],\\\"scopeName\\\":\\\"text.shell-session\\\",\\\"embeddedLangs\\\":[\\\"shellscript\\\"],\\\"aliases\\\":[\\\"console\\\"]}\"))\n\nexport default [\n...shellscript,\nlang\n]\n"], "names": ["lang", "shellsession", "shellscript"], "mappings": "mCAEA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,qlBAAqpB,CAAC,EAE7qBC,EAAA,CACf,GAAGC,EACHF,CACA", "x_google_ignoreList": [0]}