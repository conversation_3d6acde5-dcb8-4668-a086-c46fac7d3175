{"version": 3, "file": "chunk-Bp6g37R7.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.2/node_modules/@shikijs/langs/dist/codeowners.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CODEOWNERS\\\",\\\"name\\\":\\\"codeowners\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pattern\\\"},{\\\"include\\\":\\\"#owner\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.codeowners\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.codeowners\\\"}]},\\\"owner\\\":{\\\"match\\\":\\\"\\\\\\\\S*@\\\\\\\\S+\\\",\\\"name\\\":\\\"storage.type.function.codeowners\\\"},\\\"pattern\\\":{\\\"match\\\":\\\"^\\\\\\\\s*(\\\\\\\\S+)\\\",\\\"name\\\":\\\"variable.other.codeowners\\\"}},\\\"scopeName\\\":\\\"text.codeowners\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": ["lang", "codeowners"], "mappings": "AAAA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,meAA2iB,CAAC,EAEnkBC,EAAA,CACfD,CACA", "x_google_ignoreList": [0]}