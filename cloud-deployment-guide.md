# دليل نشر Waad Code في الاستضافة السحابية

## 🌐 خيارات الاستضافة المُوصى بها

### 1. قاعدة البيانات PostgreSQL

#### Supabase (مجاني + مدفوع)
```bash
# 1. إنشاء حساب في https://supabase.com
# 2. إنشاء مشروع جديد
# 3. الحصول على Database URL من Settings > Database
# 4. إضافة URL في متغيرات البيئة:
DATABASE_URL=postgresql://postgres:[password]@[host]:5432/postgres
```

#### Neon (مجاني + مدفوع)
```bash
# 1. إنشاء حساب في https://neon.tech
# 2. إنشاء قاعدة بيانات جديدة
# 3. نسخ Connection String
DATABASE_URL=postgresql://[user]:[password]@[host]/[database]
```

#### Railway (مدفوع)
```bash
# 1. إنشاء حساب في https://railway.app
# 2. إضافة PostgreSQL service
# 3. الحصول على DATABASE_URL من Variables
```

### 2. قاعدة بيانات Redis

#### Upstash Redis (مجاني + مدفوع)
```bash
# 1. إنشاء حساب في https://upstash.com
# 2. إنشاء Redis database
# 3. نسخ Redis URL
REDIS_URL=redis://:[password]@[host]:6379
```

#### Redis Cloud (مدفوع)
```bash
# 1. إنشاء حساب في https://redis.com
# 2. إنشاء subscription جديد
# 3. الحصول على connection details
```

## 🚀 خطوات النشر

### 1. إعداد متغيرات البيئة للإنتاج

إنشاء ملف `.env.production`:

```bash
# Production Database URLs
DATABASE_URL=postgresql://[production-db-url]
PRODUCTION_DATABASE_URL=postgresql://[production-db-url]
REDIS_URL=redis://[production-redis-url]

# API Keys
OPENROUTER_API_KEY=sk-or-v1-your-production-key
ANTHROPIC_API_KEY=sk-ant-your-production-key
OPENAI_API_KEY=sk-your-production-key

# Authentication
CLERK_BASE_URL=https://your-clerk-domain.com
ROO_CODE_API_URL=https://your-api-domain.com

# Environment
NODE_ENV=production
```

### 2. تشغيل المايجريشن في الإنتاج

```bash
# تعيين متغير البيئة للإنتاج
export DATABASE_URL="postgresql://[production-db-url]"

# تشغيل المايجريشن
cd packages/evals
pnpm run db:push
```

### 3. خيارات النشر

#### Vercel (للواجهة الأمامية)
```bash
# 1. ربط المشروع بـ Vercel
npx vercel

# 2. إضافة متغيرات البيئة في Vercel Dashboard
# 3. نشر المشروع
npx vercel --prod
```

#### Railway (للتطبيق الكامل)
```bash
# 1. ربط المشروع بـ Railway
railway login
railway link

# 2. إضافة متغيرات البيئة
railway variables set DATABASE_URL="postgresql://..."

# 3. نشر المشروع
railway up
```

#### Docker + VPS
```dockerfile
# Dockerfile
FROM node:20.19.2-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 🔧 إعداد CI/CD

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20.19.2'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Run tests
        run: pnpm test
      
      - name: Build project
        run: pnpm build
      
      - name: Deploy to production
        run: # أوامر النشر حسب المنصة المختارة
```

## 📊 مراقبة الأداء

### إعداد Monitoring
```bash
# إضافة مراقبة قاعدة البيانات
# Supabase: مدمج في Dashboard
# Neon: مدمج في Console
# Railway: مدمج في Dashboard

# إضافة مراقبة التطبيق
# Vercel Analytics
# Railway Metrics
# Custom monitoring with Prometheus/Grafana
```

## 🔒 الأمان

### إعدادات الأمان المُوصى بها
```bash
# 1. استخدام HTTPS فقط
# 2. تشفير متغيرات البيئة
# 3. تقييد الوصول لقاعدة البيانات
# 4. استخدام API keys منفصلة للإنتاج
# 5. تفعيل backup تلقائي لقاعدة البيانات
```

## 💰 تقدير التكاليف (شهرياً)

### الخيار الاقتصادي (مجاني/منخفض التكلفة)
- Supabase Free: $0
- Upstash Redis Free: $0
- Vercel Hobby: $0
- **المجموع: $0-20/شهر**

### الخيار المتوسط
- Supabase Pro: $25
- Upstash Redis: $10
- Railway: $20
- **المجموع: $55/شهر**

### الخيار المتقدم
- Neon Scale: $50
- Redis Cloud: $30
- VPS مخصص: $50
- **المجموع: $130/شهر**
