{"root": ["./next-env.d.ts", "./next.config.ts", "./vitest.config.ts", "./src/actions/exercises.ts", "./src/actions/heartbeat.ts", "./src/actions/runners.ts", "./src/actions/runs.ts", "./src/actions/tasks.ts", "./src/app/api/health/route.ts", "./src/app/api/runs/[id]/stream/route.ts", "./src/components/providers/index.ts", "./src/components/ui/index.ts", "./src/hooks/use-copy-run.ts", "./src/hooks/use-event-source.ts", "./src/hooks/use-open-router-models.ts", "./src/hooks/use-run-status.ts", "./src/lib/actions.ts", "./src/lib/formatters.ts", "./src/lib/schemas.ts", "./src/lib/utils.ts", "./src/lib/server/redis.ts", "./src/lib/server/sse-stream.ts", "./src/lib/server/__tests__/sse-stream.spec.ts", "./src/app/layout.tsx", "./src/app/page.tsx", "./src/app/runs/[id]/page.tsx", "./src/app/runs/[id]/run-status.tsx", "./src/app/runs/[id]/run.tsx", "./src/app/runs/[id]/task-status.tsx", "./src/app/runs/new/new-run.tsx", "./src/app/runs/new/page.tsx", "./src/app/runs/new/settings-diff.tsx", "./src/components/home/<USER>", "./src/components/home/<USER>", "./src/components/layout/header.tsx", "./src/components/layout/logo.tsx", "./src/components/providers/react-query-provider.tsx", "./src/components/providers/theme-provider.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/command.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/multi-select.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx"], "version": "5.8.3"}