{"version": 3, "file": "chunk-C8M2exoo.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+themes@3.4.2/node_modules/@shikijs/themes/dist/everforest-light.mjs"], "sourcesContent": ["/* Theme: everforest-light */\nexport default Object.freeze(JSON.parse(\"{\\\"colors\\\":{\\\"activityBar.activeBorder\\\":\\\"#93b259d0\\\",\\\"activityBar.activeFocusBorder\\\":\\\"#93b259\\\",\\\"activityBar.background\\\":\\\"#fdf6e3\\\",\\\"activityBar.border\\\":\\\"#fdf6e3\\\",\\\"activityBar.dropBackground\\\":\\\"#fdf6e3\\\",\\\"activityBar.foreground\\\":\\\"#5c6a72\\\",\\\"activityBar.inactiveForeground\\\":\\\"#939f91\\\",\\\"activityBarBadge.background\\\":\\\"#93b259\\\",\\\"activityBarBadge.foreground\\\":\\\"#fdf6e3\\\",\\\"badge.background\\\":\\\"#93b259\\\",\\\"badge.foreground\\\":\\\"#fdf6e3\\\",\\\"breadcrumb.activeSelectionForeground\\\":\\\"#5c6a72\\\",\\\"breadcrumb.focusForeground\\\":\\\"#5c6a72\\\",\\\"breadcrumb.foreground\\\":\\\"#939f91\\\",\\\"button.background\\\":\\\"#93b259\\\",\\\"button.foreground\\\":\\\"#fdf6e3\\\",\\\"button.hoverBackground\\\":\\\"#93b259d0\\\",\\\"button.secondaryBackground\\\":\\\"#efebd4\\\",\\\"button.secondaryForeground\\\":\\\"#5c6a72\\\",\\\"button.secondaryHoverBackground\\\":\\\"#e6e2cc\\\",\\\"charts.blue\\\":\\\"#3a94c5\\\",\\\"charts.foreground\\\":\\\"#5c6a72\\\",\\\"charts.green\\\":\\\"#8da101\\\",\\\"charts.orange\\\":\\\"#f57d26\\\",\\\"charts.purple\\\":\\\"#df69ba\\\",\\\"charts.red\\\":\\\"#f85552\\\",\\\"charts.yellow\\\":\\\"#dfa000\\\",\\\"checkbox.background\\\":\\\"#fdf6e3\\\",\\\"checkbox.border\\\":\\\"#e0dcc7\\\",\\\"checkbox.foreground\\\":\\\"#f57d26\\\",\\\"debugConsole.errorForeground\\\":\\\"#f85552\\\",\\\"debugConsole.infoForeground\\\":\\\"#8da101\\\",\\\"debugConsole.sourceForeground\\\":\\\"#df69ba\\\",\\\"debugConsole.warningForeground\\\":\\\"#dfa000\\\",\\\"debugConsoleInputIcon.foreground\\\":\\\"#35a77c\\\",\\\"debugIcon.breakpointCurrentStackframeForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.breakpointDisabledForeground\\\":\\\"#f1706f\\\",\\\"debugIcon.breakpointForeground\\\":\\\"#f85552\\\",\\\"debugIcon.breakpointStackframeForeground\\\":\\\"#f85552\\\",\\\"debugIcon.breakpointUnverifiedForeground\\\":\\\"#879686\\\",\\\"debugIcon.continueForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.disconnectForeground\\\":\\\"#df69ba\\\",\\\"debugIcon.pauseForeground\\\":\\\"#dfa000\\\",\\\"debugIcon.restartForeground\\\":\\\"#35a77c\\\",\\\"debugIcon.startForeground\\\":\\\"#35a77c\\\",\\\"debugIcon.stepBackForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.stepIntoForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.stepOutForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.stepOverForeground\\\":\\\"#3a94c5\\\",\\\"debugIcon.stopForeground\\\":\\\"#f85552\\\",\\\"debugTokenExpression.boolean\\\":\\\"#df69ba\\\",\\\"debugTokenExpression.error\\\":\\\"#f85552\\\",\\\"debugTokenExpression.name\\\":\\\"#3a94c5\\\",\\\"debugTokenExpression.number\\\":\\\"#df69ba\\\",\\\"debugTokenExpression.string\\\":\\\"#dfa000\\\",\\\"debugTokenExpression.value\\\":\\\"#8da101\\\",\\\"debugToolBar.background\\\":\\\"#fdf6e3\\\",\\\"descriptionForeground\\\":\\\"#939f91\\\",\\\"diffEditor.diagonalFill\\\":\\\"#e0dcc7\\\",\\\"diffEditor.insertedTextBackground\\\":\\\"#6ec39830\\\",\\\"diffEditor.removedTextBackground\\\":\\\"#f1706f30\\\",\\\"dropdown.background\\\":\\\"#fdf6e3\\\",\\\"dropdown.border\\\":\\\"#e0dcc7\\\",\\\"dropdown.foreground\\\":\\\"#879686\\\",\\\"editor.background\\\":\\\"#fdf6e3\\\",\\\"editor.findMatchBackground\\\":\\\"#f3945940\\\",\\\"editor.findMatchHighlightBackground\\\":\\\"#a4bb4a40\\\",\\\"editor.findRangeHighlightBackground\\\":\\\"#e6e2cc50\\\",\\\"editor.foldBackground\\\":\\\"#e0dcc780\\\",\\\"editor.foreground\\\":\\\"#5c6a72\\\",\\\"editor.hoverHighlightBackground\\\":\\\"#e6e2cc90\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#e6e2cc50\\\",\\\"editor.lineHighlightBackground\\\":\\\"#efebd470\\\",\\\"editor.lineHighlightBorder\\\":\\\"#e0dcc700\\\",\\\"editor.rangeHighlightBackground\\\":\\\"#efebd480\\\",\\\"editor.selectionBackground\\\":\\\"#e6e2cca0\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#e6e2cc50\\\",\\\"editor.snippetFinalTabstopHighlightBackground\\\":\\\"#a4bb4a40\\\",\\\"editor.snippetFinalTabstopHighlightBorder\\\":\\\"#fdf6e3\\\",\\\"editor.snippetTabstopHighlightBackground\\\":\\\"#efebd4\\\",\\\"editor.symbolHighlightBackground\\\":\\\"#6cb3c640\\\",\\\"editor.wordHighlightBackground\\\":\\\"#e6e2cc48\\\",\\\"editor.wordHighlightStrongBackground\\\":\\\"#e6e2cc90\\\",\\\"editorBracketHighlight.foreground1\\\":\\\"#f85552\\\",\\\"editorBracketHighlight.foreground2\\\":\\\"#dfa000\\\",\\\"editorBracketHighlight.foreground3\\\":\\\"#8da101\\\",\\\"editorBracketHighlight.foreground4\\\":\\\"#3a94c5\\\",\\\"editorBracketHighlight.foreground5\\\":\\\"#f57d26\\\",\\\"editorBracketHighlight.foreground6\\\":\\\"#df69ba\\\",\\\"editorBracketHighlight.unexpectedBracket.foreground\\\":\\\"#939f91\\\",\\\"editorBracketMatch.background\\\":\\\"#e0dcc7\\\",\\\"editorBracketMatch.border\\\":\\\"#fdf6e300\\\",\\\"editorCodeLens.foreground\\\":\\\"#a4ad9ea0\\\",\\\"editorCursor.foreground\\\":\\\"#5c6a72\\\",\\\"editorError.background\\\":\\\"#f1706f00\\\",\\\"editorError.foreground\\\":\\\"#f1706f\\\",\\\"editorGhostText.background\\\":\\\"#fdf6e300\\\",\\\"editorGhostText.foreground\\\":\\\"#a4ad9ea0\\\",\\\"editorGroup.border\\\":\\\"#efebd4\\\",\\\"editorGroup.dropBackground\\\":\\\"#e0dcc760\\\",\\\"editorGroupHeader.noTabsBackground\\\":\\\"#fdf6e3\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#fdf6e3\\\",\\\"editorGutter.addedBackground\\\":\\\"#a4bb4aa0\\\",\\\"editorGutter.background\\\":\\\"#fdf6e300\\\",\\\"editorGutter.commentRangeForeground\\\":\\\"#a4ad9e\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f1706fa0\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#6cb3c6a0\\\",\\\"editorHint.foreground\\\":\\\"#e092be\\\",\\\"editorHoverWidget.background\\\":\\\"#f4f0d9\\\",\\\"editorHoverWidget.border\\\":\\\"#e6e2cc\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#87968650\\\",\\\"editorIndentGuide.background\\\":\\\"#87968620\\\",\\\"editorInfo.background\\\":\\\"#6cb3c600\\\",\\\"editorInfo.foreground\\\":\\\"#6cb3c6\\\",\\\"editorInlayHint.background\\\":\\\"#fdf6e300\\\",\\\"editorInlayHint.foreground\\\":\\\"#a4ad9ea0\\\",\\\"editorInlayHint.parameterBackground\\\":\\\"#fdf6e300\\\",\\\"editorInlayHint.parameterForeground\\\":\\\"#a4ad9ea0\\\",\\\"editorInlayHint.typeBackground\\\":\\\"#fdf6e300\\\",\\\"editorInlayHint.typeForeground\\\":\\\"#a4ad9ea0\\\",\\\"editorLightBulb.foreground\\\":\\\"#dfa000\\\",\\\"editorLightBulbAutoFix.foreground\\\":\\\"#35a77c\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#879686e0\\\",\\\"editorLineNumber.foreground\\\":\\\"#a4ad9ea0\\\",\\\"editorLink.activeForeground\\\":\\\"#8da101\\\",\\\"editorMarkerNavigation.background\\\":\\\"#f4f0d9\\\",\\\"editorMarkerNavigationError.background\\\":\\\"#f1706f80\\\",\\\"editorMarkerNavigationInfo.background\\\":\\\"#6cb3c680\\\",\\\"editorMarkerNavigationWarning.background\\\":\\\"#e4b64980\\\",\\\"editorOverviewRuler.addedForeground\\\":\\\"#a4bb4aa0\\\",\\\"editorOverviewRuler.border\\\":\\\"#fdf6e300\\\",\\\"editorOverviewRuler.commonContentForeground\\\":\\\"#939f91\\\",\\\"editorOverviewRuler.currentContentForeground\\\":\\\"#6cb3c6\\\",\\\"editorOverviewRuler.deletedForeground\\\":\\\"#f1706fa0\\\",\\\"editorOverviewRuler.errorForeground\\\":\\\"#f85552\\\",\\\"editorOverviewRuler.findMatchForeground\\\":\\\"#6ec398\\\",\\\"editorOverviewRuler.incomingContentForeground\\\":\\\"#6ec398\\\",\\\"editorOverviewRuler.infoForeground\\\":\\\"#df69ba\\\",\\\"editorOverviewRuler.modifiedForeground\\\":\\\"#6cb3c6a0\\\",\\\"editorOverviewRuler.rangeHighlightForeground\\\":\\\"#6ec398\\\",\\\"editorOverviewRuler.selectionHighlightForeground\\\":\\\"#6ec398\\\",\\\"editorOverviewRuler.warningForeground\\\":\\\"#dfa000\\\",\\\"editorOverviewRuler.wordHighlightForeground\\\":\\\"#e0dcc7\\\",\\\"editorOverviewRuler.wordHighlightStrongForeground\\\":\\\"#e0dcc7\\\",\\\"editorRuler.foreground\\\":\\\"#e6e2cca0\\\",\\\"editorSuggestWidget.background\\\":\\\"#efebd4\\\",\\\"editorSuggestWidget.border\\\":\\\"#efebd4\\\",\\\"editorSuggestWidget.foreground\\\":\\\"#5c6a72\\\",\\\"editorSuggestWidget.highlightForeground\\\":\\\"#8da101\\\",\\\"editorSuggestWidget.selectedBackground\\\":\\\"#e6e2cc\\\",\\\"editorUnnecessaryCode.border\\\":\\\"#fdf6e3\\\",\\\"editorUnnecessaryCode.opacity\\\":\\\"#00000080\\\",\\\"editorWarning.background\\\":\\\"#e4b64900\\\",\\\"editorWarning.foreground\\\":\\\"#e4b649\\\",\\\"editorWhitespace.foreground\\\":\\\"#e6e2cc\\\",\\\"editorWidget.background\\\":\\\"#fdf6e3\\\",\\\"editorWidget.border\\\":\\\"#e0dcc7\\\",\\\"editorWidget.foreground\\\":\\\"#5c6a72\\\",\\\"errorForeground\\\":\\\"#f85552\\\",\\\"extensionBadge.remoteBackground\\\":\\\"#93b259\\\",\\\"extensionBadge.remoteForeground\\\":\\\"#fdf6e3\\\",\\\"extensionButton.prominentBackground\\\":\\\"#93b259\\\",\\\"extensionButton.prominentForeground\\\":\\\"#fdf6e3\\\",\\\"extensionButton.prominentHoverBackground\\\":\\\"#93b259d0\\\",\\\"extensionIcon.preReleaseForeground\\\":\\\"#f57d26\\\",\\\"extensionIcon.starForeground\\\":\\\"#35a77c\\\",\\\"extensionIcon.verifiedForeground\\\":\\\"#8da101\\\",\\\"focusBorder\\\":\\\"#fdf6e300\\\",\\\"foreground\\\":\\\"#879686\\\",\\\"gitDecoration.addedResourceForeground\\\":\\\"#8da101a0\\\",\\\"gitDecoration.conflictingResourceForeground\\\":\\\"#df69baa0\\\",\\\"gitDecoration.deletedResourceForeground\\\":\\\"#f85552a0\\\",\\\"gitDecoration.ignoredResourceForeground\\\":\\\"#e0dcc7\\\",\\\"gitDecoration.modifiedResourceForeground\\\":\\\"#3a94c5a0\\\",\\\"gitDecoration.stageDeletedResourceForeground\\\":\\\"#35a77ca0\\\",\\\"gitDecoration.stageModifiedResourceForeground\\\":\\\"#35a77ca0\\\",\\\"gitDecoration.submoduleResourceForeground\\\":\\\"#f57d26a0\\\",\\\"gitDecoration.untrackedResourceForeground\\\":\\\"#dfa000a0\\\",\\\"gitlens.closedPullRequestIconColor\\\":\\\"#f85552\\\",\\\"gitlens.decorations.addedForegroundColor\\\":\\\"#8da101\\\",\\\"gitlens.decorations.branchAheadForegroundColor\\\":\\\"#35a77c\\\",\\\"gitlens.decorations.branchBehindForegroundColor\\\":\\\"#f57d26\\\",\\\"gitlens.decorations.branchDivergedForegroundColor\\\":\\\"#dfa000\\\",\\\"gitlens.decorations.branchMissingUpstreamForegroundColor\\\":\\\"#f85552\\\",\\\"gitlens.decorations.branchUnpublishedForegroundColor\\\":\\\"#3a94c5\\\",\\\"gitlens.decorations.branchUpToDateForegroundColor\\\":\\\"#5c6a72\\\",\\\"gitlens.decorations.copiedForegroundColor\\\":\\\"#df69ba\\\",\\\"gitlens.decorations.deletedForegroundColor\\\":\\\"#f85552\\\",\\\"gitlens.decorations.ignoredForegroundColor\\\":\\\"#879686\\\",\\\"gitlens.decorations.modifiedForegroundColor\\\":\\\"#3a94c5\\\",\\\"gitlens.decorations.renamedForegroundColor\\\":\\\"#df69ba\\\",\\\"gitlens.decorations.untrackedForegroundColor\\\":\\\"#dfa000\\\",\\\"gitlens.gutterBackgroundColor\\\":\\\"#fdf6e3\\\",\\\"gitlens.gutterForegroundColor\\\":\\\"#5c6a72\\\",\\\"gitlens.gutterUncommittedForegroundColor\\\":\\\"#3a94c5\\\",\\\"gitlens.lineHighlightBackgroundColor\\\":\\\"#f4f0d9\\\",\\\"gitlens.lineHighlightOverviewRulerColor\\\":\\\"#93b259\\\",\\\"gitlens.mergedPullRequestIconColor\\\":\\\"#df69ba\\\",\\\"gitlens.openPullRequestIconColor\\\":\\\"#35a77c\\\",\\\"gitlens.trailingLineForegroundColor\\\":\\\"#939f91\\\",\\\"gitlens.unpublishedCommitIconColor\\\":\\\"#dfa000\\\",\\\"gitlens.unpulledChangesIconColor\\\":\\\"#f57d26\\\",\\\"gitlens.unpushlishedChangesIconColor\\\":\\\"#3a94c5\\\",\\\"icon.foreground\\\":\\\"#35a77c\\\",\\\"imagePreview.border\\\":\\\"#fdf6e3\\\",\\\"input.background\\\":\\\"#fdf6e300\\\",\\\"input.border\\\":\\\"#e0dcc7\\\",\\\"input.foreground\\\":\\\"#5c6a72\\\",\\\"input.placeholderForeground\\\":\\\"#a4ad9e\\\",\\\"inputOption.activeBorder\\\":\\\"#35a77c\\\",\\\"inputValidation.errorBackground\\\":\\\"#f1706f\\\",\\\"inputValidation.errorBorder\\\":\\\"#f85552\\\",\\\"inputValidation.errorForeground\\\":\\\"#5c6a72\\\",\\\"inputValidation.infoBackground\\\":\\\"#6cb3c6\\\",\\\"inputValidation.infoBorder\\\":\\\"#3a94c5\\\",\\\"inputValidation.infoForeground\\\":\\\"#5c6a72\\\",\\\"inputValidation.warningBackground\\\":\\\"#e4b649\\\",\\\"inputValidation.warningBorder\\\":\\\"#dfa000\\\",\\\"inputValidation.warningForeground\\\":\\\"#5c6a72\\\",\\\"issues.closed\\\":\\\"#f85552\\\",\\\"issues.open\\\":\\\"#35a77c\\\",\\\"keybindingLabel.background\\\":\\\"#fdf6e300\\\",\\\"keybindingLabel.border\\\":\\\"#f4f0d9\\\",\\\"keybindingLabel.bottomBorder\\\":\\\"#efebd4\\\",\\\"keybindingLabel.foreground\\\":\\\"#5c6a72\\\",\\\"keybindingTable.headerBackground\\\":\\\"#efebd4\\\",\\\"keybindingTable.rowsBackground\\\":\\\"#f4f0d9\\\",\\\"list.activeSelectionBackground\\\":\\\"#e6e2cc80\\\",\\\"list.activeSelectionForeground\\\":\\\"#5c6a72\\\",\\\"list.dropBackground\\\":\\\"#f4f0d980\\\",\\\"list.errorForeground\\\":\\\"#f85552\\\",\\\"list.focusBackground\\\":\\\"#e6e2cc80\\\",\\\"list.focusForeground\\\":\\\"#5c6a72\\\",\\\"list.highlightForeground\\\":\\\"#8da101\\\",\\\"list.hoverBackground\\\":\\\"#fdf6e300\\\",\\\"list.hoverForeground\\\":\\\"#5c6a72\\\",\\\"list.inactiveFocusBackground\\\":\\\"#e6e2cc60\\\",\\\"list.inactiveSelectionBackground\\\":\\\"#e6e2cc80\\\",\\\"list.inactiveSelectionForeground\\\":\\\"#879686\\\",\\\"list.invalidItemForeground\\\":\\\"#f1706f\\\",\\\"list.warningForeground\\\":\\\"#dfa000\\\",\\\"menu.background\\\":\\\"#fdf6e3\\\",\\\"menu.foreground\\\":\\\"#879686\\\",\\\"menu.selectionBackground\\\":\\\"#f4f0d9\\\",\\\"menu.selectionForeground\\\":\\\"#5c6a72\\\",\\\"menubar.selectionBackground\\\":\\\"#fdf6e3\\\",\\\"menubar.selectionBorder\\\":\\\"#fdf6e3\\\",\\\"merge.border\\\":\\\"#fdf6e300\\\",\\\"merge.currentContentBackground\\\":\\\"#6cb3c640\\\",\\\"merge.currentHeaderBackground\\\":\\\"#6cb3c680\\\",\\\"merge.incomingContentBackground\\\":\\\"#6ec39840\\\",\\\"merge.incomingHeaderBackground\\\":\\\"#6ec39880\\\",\\\"minimap.errorHighlight\\\":\\\"#f1706f80\\\",\\\"minimap.findMatchHighlight\\\":\\\"#6ec39860\\\",\\\"minimap.selectionHighlight\\\":\\\"#e0dcc7f0\\\",\\\"minimap.warningHighlight\\\":\\\"#e4b64980\\\",\\\"minimapGutter.addedBackground\\\":\\\"#a4bb4aa0\\\",\\\"minimapGutter.deletedBackground\\\":\\\"#f1706fa0\\\",\\\"minimapGutter.modifiedBackground\\\":\\\"#6cb3c6a0\\\",\\\"notebook.cellBorderColor\\\":\\\"#e0dcc7\\\",\\\"notebook.cellHoverBackground\\\":\\\"#fdf6e3\\\",\\\"notebook.cellStatusBarItemHoverBackground\\\":\\\"#f4f0d9\\\",\\\"notebook.cellToolbarSeparator\\\":\\\"#e0dcc7\\\",\\\"notebook.focusedCellBackground\\\":\\\"#fdf6e3\\\",\\\"notebook.focusedCellBorder\\\":\\\"#e0dcc7\\\",\\\"notebook.focusedEditorBorder\\\":\\\"#e0dcc7\\\",\\\"notebook.focusedRowBorder\\\":\\\"#e0dcc7\\\",\\\"notebook.inactiveFocusedCellBorder\\\":\\\"#e0dcc7\\\",\\\"notebook.outputContainerBackgroundColor\\\":\\\"#f4f0d9\\\",\\\"notebook.selectedCellBorder\\\":\\\"#e0dcc7\\\",\\\"notebookStatusErrorIcon.foreground\\\":\\\"#f85552\\\",\\\"notebookStatusRunningIcon.foreground\\\":\\\"#3a94c5\\\",\\\"notebookStatusSuccessIcon.foreground\\\":\\\"#8da101\\\",\\\"notificationCenterHeader.background\\\":\\\"#efebd4\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#5c6a72\\\",\\\"notificationLink.foreground\\\":\\\"#8da101\\\",\\\"notifications.background\\\":\\\"#fdf6e3\\\",\\\"notifications.foreground\\\":\\\"#5c6a72\\\",\\\"notificationsErrorIcon.foreground\\\":\\\"#f85552\\\",\\\"notificationsInfoIcon.foreground\\\":\\\"#3a94c5\\\",\\\"notificationsWarningIcon.foreground\\\":\\\"#dfa000\\\",\\\"panel.background\\\":\\\"#fdf6e3\\\",\\\"panel.border\\\":\\\"#fdf6e3\\\",\\\"panelInput.border\\\":\\\"#e0dcc7\\\",\\\"panelSection.border\\\":\\\"#efebd4\\\",\\\"panelSectionHeader.background\\\":\\\"#fdf6e3\\\",\\\"panelTitle.activeBorder\\\":\\\"#93b259d0\\\",\\\"panelTitle.activeForeground\\\":\\\"#5c6a72\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#939f91\\\",\\\"peekView.border\\\":\\\"#e6e2cc\\\",\\\"peekViewEditor.background\\\":\\\"#f4f0d9\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#e4b64950\\\",\\\"peekViewEditorGutter.background\\\":\\\"#f4f0d9\\\",\\\"peekViewResult.background\\\":\\\"#f4f0d9\\\",\\\"peekViewResult.fileForeground\\\":\\\"#5c6a72\\\",\\\"peekViewResult.lineForeground\\\":\\\"#879686\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#e4b64950\\\",\\\"peekViewResult.selectionBackground\\\":\\\"#6ec39850\\\",\\\"peekViewResult.selectionForeground\\\":\\\"#5c6a72\\\",\\\"peekViewTitle.background\\\":\\\"#e6e2cc\\\",\\\"peekViewTitleDescription.foreground\\\":\\\"#5c6a72\\\",\\\"peekViewTitleLabel.foreground\\\":\\\"#8da101\\\",\\\"pickerGroup.border\\\":\\\"#93b2591a\\\",\\\"pickerGroup.foreground\\\":\\\"#5c6a72\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#f57d26\\\",\\\"problemsErrorIcon.foreground\\\":\\\"#f85552\\\",\\\"problemsInfoIcon.foreground\\\":\\\"#3a94c5\\\",\\\"problemsWarningIcon.foreground\\\":\\\"#dfa000\\\",\\\"progressBar.background\\\":\\\"#93b259\\\",\\\"quickInputTitle.background\\\":\\\"#f4f0d9\\\",\\\"rust_analyzer.inlayHints.background\\\":\\\"#fdf6e300\\\",\\\"rust_analyzer.inlayHints.foreground\\\":\\\"#a4ad9ea0\\\",\\\"rust_analyzer.syntaxTreeBorder\\\":\\\"#f85552\\\",\\\"sash.hoverBorder\\\":\\\"#e6e2cc\\\",\\\"scrollbar.shadow\\\":\\\"#3c474d20\\\",\\\"scrollbarSlider.activeBackground\\\":\\\"#879686\\\",\\\"scrollbarSlider.background\\\":\\\"#e0dcc780\\\",\\\"scrollbarSlider.hoverBackground\\\":\\\"#e0dcc7\\\",\\\"selection.background\\\":\\\"#e6e2ccc0\\\",\\\"settings.checkboxBackground\\\":\\\"#fdf6e3\\\",\\\"settings.checkboxBorder\\\":\\\"#e0dcc7\\\",\\\"settings.checkboxForeground\\\":\\\"#f57d26\\\",\\\"settings.dropdownBackground\\\":\\\"#fdf6e3\\\",\\\"settings.dropdownBorder\\\":\\\"#e0dcc7\\\",\\\"settings.dropdownForeground\\\":\\\"#35a77c\\\",\\\"settings.focusedRowBackground\\\":\\\"#f4f0d9\\\",\\\"settings.headerForeground\\\":\\\"#879686\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#a4ad9e\\\",\\\"settings.numberInputBackground\\\":\\\"#fdf6e3\\\",\\\"settings.numberInputBorder\\\":\\\"#e0dcc7\\\",\\\"settings.numberInputForeground\\\":\\\"#df69ba\\\",\\\"settings.rowHoverBackground\\\":\\\"#f4f0d9\\\",\\\"settings.textInputBackground\\\":\\\"#fdf6e3\\\",\\\"settings.textInputBorder\\\":\\\"#e0dcc7\\\",\\\"settings.textInputForeground\\\":\\\"#3a94c5\\\",\\\"sideBar.background\\\":\\\"#fdf6e3\\\",\\\"sideBar.foreground\\\":\\\"#939f91\\\",\\\"sideBarSectionHeader.background\\\":\\\"#fdf6e300\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#879686\\\",\\\"sideBarTitle.foreground\\\":\\\"#879686\\\",\\\"statusBar.background\\\":\\\"#fdf6e3\\\",\\\"statusBar.border\\\":\\\"#fdf6e3\\\",\\\"statusBar.debuggingBackground\\\":\\\"#fdf6e3\\\",\\\"statusBar.debuggingForeground\\\":\\\"#f57d26\\\",\\\"statusBar.foreground\\\":\\\"#879686\\\",\\\"statusBar.noFolderBackground\\\":\\\"#fdf6e3\\\",\\\"statusBar.noFolderBorder\\\":\\\"#fdf6e3\\\",\\\"statusBar.noFolderForeground\\\":\\\"#879686\\\",\\\"statusBarItem.activeBackground\\\":\\\"#e6e2cc70\\\",\\\"statusBarItem.errorBackground\\\":\\\"#fdf6e3\\\",\\\"statusBarItem.errorForeground\\\":\\\"#f85552\\\",\\\"statusBarItem.hoverBackground\\\":\\\"#e6e2cca0\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#fdf6e3\\\",\\\"statusBarItem.prominentForeground\\\":\\\"#5c6a72\\\",\\\"statusBarItem.prominentHoverBackground\\\":\\\"#e6e2cca0\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#fdf6e3\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#879686\\\",\\\"statusBarItem.warningBackground\\\":\\\"#fdf6e3\\\",\\\"statusBarItem.warningForeground\\\":\\\"#dfa000\\\",\\\"symbolIcon.arrayForeground\\\":\\\"#3a94c5\\\",\\\"symbolIcon.booleanForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.classForeground\\\":\\\"#dfa000\\\",\\\"symbolIcon.colorForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.constantForeground\\\":\\\"#35a77c\\\",\\\"symbolIcon.constructorForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.enumeratorForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.enumeratorMemberForeground\\\":\\\"#35a77c\\\",\\\"symbolIcon.eventForeground\\\":\\\"#dfa000\\\",\\\"symbolIcon.fieldForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.fileForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.folderForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.functionForeground\\\":\\\"#8da101\\\",\\\"symbolIcon.interfaceForeground\\\":\\\"#dfa000\\\",\\\"symbolIcon.keyForeground\\\":\\\"#8da101\\\",\\\"symbolIcon.keywordForeground\\\":\\\"#f85552\\\",\\\"symbolIcon.methodForeground\\\":\\\"#8da101\\\",\\\"symbolIcon.moduleForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.namespaceForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.nullForeground\\\":\\\"#35a77c\\\",\\\"symbolIcon.numberForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.objectForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.operatorForeground\\\":\\\"#f57d26\\\",\\\"symbolIcon.packageForeground\\\":\\\"#df69ba\\\",\\\"symbolIcon.propertyForeground\\\":\\\"#35a77c\\\",\\\"symbolIcon.referenceForeground\\\":\\\"#3a94c5\\\",\\\"symbolIcon.snippetForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.stringForeground\\\":\\\"#8da101\\\",\\\"symbolIcon.structForeground\\\":\\\"#dfa000\\\",\\\"symbolIcon.textForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.typeParameterForeground\\\":\\\"#35a77c\\\",\\\"symbolIcon.unitForeground\\\":\\\"#5c6a72\\\",\\\"symbolIcon.variableForeground\\\":\\\"#3a94c5\\\",\\\"tab.activeBackground\\\":\\\"#fdf6e3\\\",\\\"tab.activeBorder\\\":\\\"#93b259d0\\\",\\\"tab.activeForeground\\\":\\\"#5c6a72\\\",\\\"tab.border\\\":\\\"#fdf6e3\\\",\\\"tab.hoverBackground\\\":\\\"#fdf6e3\\\",\\\"tab.hoverForeground\\\":\\\"#5c6a72\\\",\\\"tab.inactiveBackground\\\":\\\"#fdf6e3\\\",\\\"tab.inactiveForeground\\\":\\\"#a4ad9e\\\",\\\"tab.lastPinnedBorder\\\":\\\"#93b259d0\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#939f91\\\",\\\"tab.unfocusedActiveForeground\\\":\\\"#879686\\\",\\\"tab.unfocusedHoverForeground\\\":\\\"#5c6a72\\\",\\\"tab.unfocusedInactiveForeground\\\":\\\"#a4ad9e\\\",\\\"terminal.ansiBlack\\\":\\\"#5c6a72\\\",\\\"terminal.ansiBlue\\\":\\\"#3a94c5\\\",\\\"terminal.ansiBrightBlack\\\":\\\"#5c6a72\\\",\\\"terminal.ansiBrightBlue\\\":\\\"#3a94c5\\\",\\\"terminal.ansiBrightCyan\\\":\\\"#35a77c\\\",\\\"terminal.ansiBrightGreen\\\":\\\"#8da101\\\",\\\"terminal.ansiBrightMagenta\\\":\\\"#df69ba\\\",\\\"terminal.ansiBrightRed\\\":\\\"#f85552\\\",\\\"terminal.ansiBrightWhite\\\":\\\"#f4f0d9\\\",\\\"terminal.ansiBrightYellow\\\":\\\"#dfa000\\\",\\\"terminal.ansiCyan\\\":\\\"#35a77c\\\",\\\"terminal.ansiGreen\\\":\\\"#8da101\\\",\\\"terminal.ansiMagenta\\\":\\\"#df69ba\\\",\\\"terminal.ansiRed\\\":\\\"#f85552\\\",\\\"terminal.ansiWhite\\\":\\\"#939f91\\\",\\\"terminal.ansiYellow\\\":\\\"#dfa000\\\",\\\"terminal.foreground\\\":\\\"#5c6a72\\\",\\\"terminalCursor.foreground\\\":\\\"#5c6a72\\\",\\\"testing.iconErrored\\\":\\\"#f85552\\\",\\\"testing.iconFailed\\\":\\\"#f85552\\\",\\\"testing.iconPassed\\\":\\\"#35a77c\\\",\\\"testing.iconQueued\\\":\\\"#3a94c5\\\",\\\"testing.iconSkipped\\\":\\\"#df69ba\\\",\\\"testing.iconUnset\\\":\\\"#dfa000\\\",\\\"testing.runAction\\\":\\\"#35a77c\\\",\\\"textBlockQuote.background\\\":\\\"#f4f0d9\\\",\\\"textBlockQuote.border\\\":\\\"#e6e2cc\\\",\\\"textCodeBlock.background\\\":\\\"#f4f0d9\\\",\\\"textLink.activeForeground\\\":\\\"#8da101c0\\\",\\\"textLink.foreground\\\":\\\"#8da101\\\",\\\"textPreformat.foreground\\\":\\\"#dfa000\\\",\\\"titleBar.activeBackground\\\":\\\"#fdf6e3\\\",\\\"titleBar.activeForeground\\\":\\\"#879686\\\",\\\"titleBar.border\\\":\\\"#fdf6e3\\\",\\\"titleBar.inactiveBackground\\\":\\\"#fdf6e3\\\",\\\"titleBar.inactiveForeground\\\":\\\"#a4ad9e\\\",\\\"toolbar.hoverBackground\\\":\\\"#f4f0d9\\\",\\\"tree.indentGuidesStroke\\\":\\\"#a4ad9e\\\",\\\"walkThrough.embeddedEditorBackground\\\":\\\"#f4f0d9\\\",\\\"welcomePage.buttonBackground\\\":\\\"#f4f0d9\\\",\\\"welcomePage.buttonHoverBackground\\\":\\\"#f4f0d9a0\\\",\\\"welcomePage.progress.foreground\\\":\\\"#8da101\\\",\\\"welcomePage.tileHoverBackground\\\":\\\"#f4f0d9\\\",\\\"widget.shadow\\\":\\\"#3c474d20\\\"},\\\"displayName\\\":\\\"Everforest Light\\\",\\\"name\\\":\\\"everforest-light\\\",\\\"semanticHighlighting\\\":true,\\\"semanticTokenColors\\\":{\\\"class:python\\\":\\\"#35a77c\\\",\\\"class:typescript\\\":\\\"#35a77c\\\",\\\"class:typescriptreact\\\":\\\"#35a77c\\\",\\\"enum:typescript\\\":\\\"#df69ba\\\",\\\"enum:typescriptreact\\\":\\\"#df69ba\\\",\\\"enumMember:typescript\\\":\\\"#3a94c5\\\",\\\"enumMember:typescriptreact\\\":\\\"#3a94c5\\\",\\\"interface:typescript\\\":\\\"#35a77c\\\",\\\"interface:typescriptreact\\\":\\\"#35a77c\\\",\\\"intrinsic:python\\\":\\\"#df69ba\\\",\\\"macro:rust\\\":\\\"#35a77c\\\",\\\"memberOperatorOverload\\\":\\\"#f57d26\\\",\\\"module:python\\\":\\\"#3a94c5\\\",\\\"namespace:rust\\\":\\\"#df69ba\\\",\\\"namespace:typescript\\\":\\\"#df69ba\\\",\\\"namespace:typescriptreact\\\":\\\"#df69ba\\\",\\\"operatorOverload\\\":\\\"#f57d26\\\",\\\"property.defaultLibrary:javascript\\\":\\\"#df69ba\\\",\\\"property.defaultLibrary:javascriptreact\\\":\\\"#df69ba\\\",\\\"property.defaultLibrary:typescript\\\":\\\"#df69ba\\\",\\\"property.defaultLibrary:typescriptreact\\\":\\\"#df69ba\\\",\\\"selfKeyword:rust\\\":\\\"#df69ba\\\",\\\"variable.defaultLibrary:javascript\\\":\\\"#df69ba\\\",\\\"variable.defaultLibrary:javascriptreact\\\":\\\"#df69ba\\\",\\\"variable.defaultLibrary:typescript\\\":\\\"#df69ba\\\",\\\"variable.defaultLibrary:typescriptreact\\\":\\\"#df69ba\\\"},\\\"tokenColors\\\":[{\\\"scope\\\":\\\"keyword, storage.type.function, storage.type.class, storage.type.enum, storage.type.interface, storage.type.property, keyword.operator.new, keyword.operator.expression, keyword.operator.new, keyword.operator.delete, storage.type.extends\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.other.debugger\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage, modifier, keyword.var, entity.name.tag, keyword.control.case, keyword.control.switch\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"keyword.operator\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"string, punctuation.definition.string.end, punctuation.definition.string.begin, punctuation.definition.string.template.begin, punctuation.definition.string.template.end\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"constant.character.escape, punctuation.quasi.element, punctuation.definition.template-expression, punctuation.section.embedded, storage.type.format, constant.other.placeholder, constant.other.placeholder, variable.interpolation\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.function, support.function, meta.function, meta.function-call, meta.definition.method\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.control.at-rule, keyword.control.import, keyword.control.export, storage.type.namespace, punctuation.decorator, keyword.control.directive, keyword.preprocessor, punctuation.definition.preprocessor, punctuation.definition.directive, keyword.other.import, keyword.other.package, entity.name.type.namespace, entity.name.scope-resolution, keyword.other.using, keyword.package, keyword.import, keyword.map\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type.annotation\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.label, constant.other.label\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"support.module, support.node, support.other.module, support.type.object.module, entity.name.type.module, entity.name.type.class.module, keyword.control.module\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type, support.type, entity.name.type, keyword.type\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"entity.name.type.class, support.class, entity.name.class, entity.other.inherited-class, storage.class\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"constant.numeric\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"constant.language.boolean\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.function.preprocessor\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"variable.language.this, variable.language.self, variable.language.super, keyword.other.this, variable.language.special, constant.language.null, constant.language.undefined, constant.language.nan\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"constant.language, support.constant\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"variable, support.variable, meta.definition.variable\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"variable.object.property, support.variable.property, variable.other.property, variable.other.object.property, variable.other.enummember, variable.other.member, meta.object-literal.key\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation, meta.brace, meta.delimiter, meta.bracket\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"heading.1.markdown, markup.heading.setext.1.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"heading.2.markdown, markup.heading.setext.2.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"heading.3.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"heading.4.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"heading.5.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"heading.6.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.definition.heading.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"string.other.link.title.markdown, constant.other.reference.link.markdown, string.other.link.description.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"markup.underline.link.image.markdown, markup.underline.link.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\",\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"punctuation.definition.string.begin.markdown, punctuation.definition.string.end.markdown, punctuation.definition.italic.markdown, punctuation.definition.quote.begin.markdown, punctuation.definition.metadata.markdown, punctuation.separator.key-value.markdown, punctuation.definition.constant.markdown\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"punctuation.definition.bold.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"meta.separator.markdown, punctuation.definition.constant.begin.markdown, punctuation.definition.constant.end.markdown\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"markup.italic\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"}},{\\\"scope\\\":\\\"markup.bold\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"}},{\\\"scope\\\":\\\"markup.bold markup.italic, markup.italic markup.bold\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic bold\\\"}},{\\\"scope\\\":\\\"punctuation.definition.markdown, punctuation.definition.raw.markdown\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"fenced_code.block.language\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"markup.fenced_code.block.markdown, markup.inline.raw.string.markdown\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"punctuation.definition.heading.restructuredtext\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"punctuation.definition.field.restructuredtext, punctuation.separator.key-value.restructuredtext, punctuation.definition.directive.restructuredtext, punctuation.definition.constant.restructuredtext, punctuation.definition.italic.restructuredtext, punctuation.definition.table.restructuredtext\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"punctuation.definition.bold.restructuredtext\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"regular\\\",\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"entity.name.tag.restructuredtext, punctuation.definition.link.restructuredtext, punctuation.definition.raw.restructuredtext, punctuation.section.raw.restructuredtext\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"constant.other.footnote.link.restructuredtext\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"support.directive.restructuredtext\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"entity.name.directive.restructuredtext, markup.raw.restructuredtext, markup.raw.inner.restructuredtext, string.other.link.title.restructuredtext\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"punctuation.definition.function.latex, punctuation.definition.function.tex, punctuation.definition.keyword.latex, constant.character.newline.tex, punctuation.definition.keyword.tex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"support.function.be.latex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"support.function.section.latex, keyword.control.table.cell.latex, keyword.control.table.newline.latex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"support.class.latex, variable.parameter.latex, variable.parameter.function.latex, variable.parameter.definition.label.latex, constant.other.reference.label.latex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"keyword.control.preamble.latex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.separator.namespace.xml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"entity.name.tag.html, entity.name.tag.xml, entity.name.tag.localname.xml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.html, entity.other.attribute-name.xml, entity.other.attribute-name.localname.xml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.html, string.quoted.single.html, punctuation.definition.string.begin.html, punctuation.definition.string.end.html, punctuation.separator.key-value.html, punctuation.definition.string.begin.xml, punctuation.definition.string.end.xml, string.quoted.double.xml, string.quoted.single.xml, punctuation.definition.tag.begin.html, punctuation.definition.tag.end.html, punctuation.definition.tag.xml, meta.tag.xml, meta.tag.preprocessor.xml, meta.tag.other.html, meta.tag.block.any.html, meta.tag.inline.any.html\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"variable.language.documentroot.xml, meta.tag.sgml.doctype.xml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"storage.type.proto\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.proto.syntax, string.quoted.single.proto.syntax, string.quoted.double.proto, string.quoted.single.proto\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.class.proto, entity.name.class.message.proto\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"punctuation.definition.entity.css, punctuation.separator.key-value.css, punctuation.terminator.rule.css, punctuation.separator.list.comma.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.class.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.other.unit\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.pseudo-class.css, entity.other.attribute-name.pseudo-element.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.single.css, string.quoted.double.css, support.constant.property-value.css, meta.property-value.css, punctuation.definition.string.begin.css, punctuation.definition.string.end.css, constant.numeric.css, support.constant.font-name.css, variable.parameter.keyframe-list.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"support.type.property-name.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"support.type.vendored.property-name.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"entity.name.tag.css, entity.other.keyframe-offset.css, punctuation.definition.keyword.css, keyword.control.at-rule.keyframes.css, meta.selector.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.definition.entity.scss, punctuation.separator.key-value.scss, punctuation.terminator.rule.scss, punctuation.separator.list.comma.scss\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.control.at-rule.keyframes.scss\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"punctuation.definition.interpolation.begin.bracket.curly.scss, punctuation.definition.interpolation.end.bracket.curly.scss\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"punctuation.definition.string.begin.scss, punctuation.definition.string.end.scss, string.quoted.double.scss, string.quoted.single.scss, constant.character.css.sass, meta.property-value.scss\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.control.at-rule.include.scss, keyword.control.at-rule.use.scss, keyword.control.at-rule.mixin.scss, keyword.control.at-rule.extend.scss, keyword.control.at-rule.import.scss\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"meta.function.stylus\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"entity.name.function.stylus\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.unquoted.js\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.accessor.js, punctuation.separator.key-value.js, punctuation.separator.label.js, keyword.operator.accessor.js\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"punctuation.definition.block.tag.jsdoc\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage.type.js, storage.type.function.arrow.js\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"JSXNested\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.definition.tag.jsx, entity.other.attribute-name.jsx, punctuation.definition.tag.begin.js.jsx, punctuation.definition.tag.end.js.jsx, entity.other.attribute-name.js.jsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.type.module.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.operator.type.annotation.ts, punctuation.accessor.ts, punctuation.separator.key-value.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"punctuation.definition.tag.directive.ts, entity.other.attribute-name.directive.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.type.ts, entity.name.type.interface.ts, entity.other.inherited-class.ts, entity.name.type.alias.ts, entity.name.type.class.ts, entity.name.type.enum.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type.ts, storage.type.function.arrow.ts, storage.type.type.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.name.type.module.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.control.import.ts, keyword.control.export.ts, storage.type.namespace.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.type.module.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.operator.type.annotation.tsx, punctuation.accessor.tsx, punctuation.separator.key-value.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"punctuation.definition.tag.directive.tsx, entity.other.attribute-name.directive.tsx, punctuation.definition.tag.begin.tsx, punctuation.definition.tag.end.tsx, entity.other.attribute-name.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.type.tsx, entity.name.type.interface.tsx, entity.other.inherited-class.tsx, entity.name.type.alias.tsx, entity.name.type.class.tsx, entity.name.type.enum.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.type.module.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.control.import.tsx, keyword.control.export.tsx, storage.type.namespace.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"storage.type.tsx, storage.type.function.arrow.tsx, storage.type.type.tsx, support.class.component.tsx\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"storage.type.function.coffee\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"meta.type-signature.purescript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.other.double-colon.purescript, keyword.other.arrow.purescript, keyword.other.big-arrow.purescript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.name.function.purescript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.single.purescript, string.quoted.double.purescript, punctuation.definition.string.begin.purescript, punctuation.definition.string.end.purescript, string.quoted.triple.purescript, entity.name.type.purescript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"support.other.module.purescript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.dot.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"storage.type.primitive.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"support.class.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"entity.name.function.dart, string.interpolated.single.dart, string.interpolated.double.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"variable.language.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.other.import.dart, storage.type.annotation.dart\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.class.pug\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage.type.function.pug\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.tag.pug\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.tag.pug, storage.type.import.include.pug\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"meta.function-call.c, storage.modifier.array.bracket.square.c, meta.function.definition.parameters.c\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.separator.dot-access.c, constant.character.escape.line-continuation.c\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.control.directive.include.c, punctuation.definition.directive.c, keyword.control.directive.pragma.c, keyword.control.directive.line.c, keyword.control.directive.define.c, keyword.control.directive.conditional.c, keyword.control.directive.diagnostic.error.c, keyword.control.directive.undef.c, keyword.control.directive.conditional.ifdef.c, keyword.control.directive.endif.c, keyword.control.directive.conditional.ifndef.c, keyword.control.directive.conditional.if.c, keyword.control.directive.else.c\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"punctuation.separator.pointer-access.c\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.member.c\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"meta.function-call.cpp, storage.modifier.array.bracket.square.cpp, meta.function.definition.parameters.cpp, meta.body.function.definition.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.separator.dot-access.cpp, constant.character.escape.line-continuation.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.control.directive.include.cpp, punctuation.definition.directive.cpp, keyword.control.directive.pragma.cpp, keyword.control.directive.line.cpp, keyword.control.directive.define.cpp, keyword.control.directive.conditional.cpp, keyword.control.directive.diagnostic.error.cpp, keyword.control.directive.undef.cpp, keyword.control.directive.conditional.ifdef.cpp, keyword.control.directive.endif.cpp, keyword.control.directive.conditional.ifndef.cpp, keyword.control.directive.conditional.if.cpp, keyword.control.directive.else.cpp, storage.type.namespace.definition.cpp, keyword.other.using.directive.cpp, storage.type.struct.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"punctuation.separator.pointer-access.cpp, punctuation.section.angle-brackets.begin.template.call.cpp, punctuation.section.angle-brackets.end.template.call.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.member.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.other.using.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.type.cs, constant.character.escape.cs, punctuation.definition.interpolation.begin.cs, punctuation.definition.interpolation.end.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.cs, string.quoted.single.cs, punctuation.definition.string.begin.cs, punctuation.definition.string.end.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"variable.other.object.property.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.type.namespace.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"keyword.symbol.fsharp, constant.language.unit.fsharp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.format.specifier.fsharp, entity.name.type.fsharp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.fsharp, string.quoted.single.fsharp, punctuation.definition.string.begin.fsharp, punctuation.definition.string.end.fsharp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.section.fsharp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"support.function.attribute.fsharp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.separator.java, punctuation.separator.period.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.other.import.java, keyword.other.package.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage.type.function.arrow.java, keyword.control.ternary.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.property.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"variable.language.wildcard.java, storage.modifier.import.java, storage.type.annotation.java, punctuation.definition.annotation.java, storage.modifier.package.java, entity.name.type.module.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"keyword.other.import.kotlin\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage.type.kotlin\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"constant.language.kotlin\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.package.kotlin, storage.type.annotation.kotlin\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.package.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"constant.language.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"entity.name.import.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"string.quoted.double.scala, string.quoted.single.scala, punctuation.definition.string.begin.scala, punctuation.definition.string.end.scala, string.quoted.double.interpolated.scala, string.quoted.single.interpolated.scala, string.quoted.triple.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.class, entity.other.inherited-class.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"keyword.declaration.stable.scala, keyword.other.arrow.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"keyword.other.import.scala\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.operator.navigation.groovy, meta.method.body.java, meta.definition.method.groovy, meta.definition.method.signature.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.separator.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.other.import.groovy, keyword.other.package.groovy, keyword.other.import.static.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"storage.type.def.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.interpolated.groovy, meta.method.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"storage.modifier.import.groovy, storage.modifier.package.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type.annotation.groovy\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"keyword.type.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"entity.name.package.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.import.go, keyword.package.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.type.mod.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.operator.path.rust, keyword.operator.member-access.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"storage.type.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"support.constant.core.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"meta.attribute.rust, variable.language.rust, storage.type.module.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"meta.function-call.swift, support.function.any-method.swift\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"support.variable.swift\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.operator.class.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"storage.type.trait.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"constant.language.php, support.other.namespace.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type.modifier.access.control.public.cpp, storage.type.modifier.access.control.private.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.control.import.include.php, storage.type.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"meta.function-call.arguments.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.definition.decorator.python, punctuation.separator.period.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"constant.language.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.control.import.python, keyword.control.import.from.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"constant.language.lua\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.class.lua\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"meta.function.method.with-arguments.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"punctuation.separator.method.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.control.pseudo-method.ruby, storage.type.variable.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"keyword.other.special-method.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.control.module.ruby, punctuation.definition.constant.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"string.regexp.character-class.ruby,string.regexp.interpolated.ruby,punctuation.definition.character-class.ruby,string.regexp.group.ruby, punctuation.section.regexp.ruby, punctuation.definition.group.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"variable.other.constant.ruby\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.other.arrow.haskell, keyword.other.big-arrow.haskell, keyword.other.double-colon.haskell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"storage.type.haskell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"constant.other.haskell, string.quoted.double.haskell, string.quoted.single.haskell, punctuation.definition.string.begin.haskell, punctuation.definition.string.end.haskell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.function.haskell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"entity.name.namespace, meta.preprocessor.haskell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.control.import.julia, keyword.control.export.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.storage.modifier.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"constant.language.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"support.function.macro.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"keyword.other.period.elm\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"storage.type.elm\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"keyword.other.r\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"entity.name.function.r, variable.function.r\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"constant.language.r\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.namespace.r\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.separator.module-function.erlang, punctuation.section.directive.begin.erlang\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"keyword.control.directive.erlang, keyword.control.directive.define.erlang\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"entity.name.type.class.module.erlang\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.erlang, string.quoted.single.erlang, punctuation.definition.string.begin.erlang, punctuation.definition.string.end.erlang\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.control.directive.export.erlang, keyword.control.directive.module.erlang, keyword.control.directive.import.erlang, keyword.control.directive.behaviour.erlang\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"variable.other.readwrite.module.elixir, punctuation.definition.variable.elixir\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"constant.language.elixir\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"keyword.control.module.elixir\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.type.value-signature.ocaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.other.ocaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"constant.language.variant.ocaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.type.sub.perl, storage.type.declare.routine.perl\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"meta.function.lisp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"storage.type.function-type.lisp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"keyword.constant.lisp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.function.lisp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"constant.keyword.clojure, support.variable.clojure, meta.definition.variable.clojure\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.global.clojure\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.function.clojure\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"meta.scope.if-block.shell, meta.scope.group.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"support.function.builtin.shell, entity.name.function.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.shell, string.quoted.single.shell, punctuation.definition.string.begin.shell, punctuation.definition.string.end.shell, string.unquoted.heredoc.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.control.heredoc-token.shell, variable.other.normal.shell, punctuation.definition.variable.shell, variable.other.special.shell, variable.other.positional.shell, variable.other.bracket.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"support.function.builtin.fish\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"support.function.unix.fish\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.normal.fish, punctuation.definition.variable.fish, variable.other.fixed.fish, variable.other.special.fish\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"string.quoted.double.fish, punctuation.definition.string.end.fish, punctuation.definition.string.begin.fish, string.quoted.single.fish\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"constant.character.escape.single.fish\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.definition.variable.powershell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"entity.name.function.powershell, support.function.attribute.powershell, support.function.powershell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.single.powershell, string.quoted.double.powershell, punctuation.definition.string.begin.powershell, punctuation.definition.string.end.powershell, string.quoted.double.heredoc.powershell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"variable.other.member.powershell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"string.unquoted.alias.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#5c6a72\\\"}},{\\\"scope\\\":\\\"keyword.type.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"entity.name.fragment.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.function.target.makefile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"variable.other.makefile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"meta.scope.prerequisites.makefile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"string.source.cmake\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.source.cmake\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"storage.source.cmake\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.definition.map.viml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"storage.type.map.viml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"constant.character.map.viml, constant.character.map.key.viml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"constant.character.map.special.viml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"constant.language.tmux, constant.numeric.tmux\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"entity.name.function.package-manager.dockerfile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"keyword.operator.flag.dockerfile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.double.dockerfile, string.quoted.single.dockerfile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"constant.character.escape.dockerfile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"entity.name.type.base-image.dockerfile, entity.name.image.dockerfile\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"punctuation.definition.separator.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"markup.deleted.diff, punctuation.definition.deleted.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"meta.diff.range.context, punctuation.definition.range.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"meta.diff.header.from-file\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"markup.inserted.diff, punctuation.definition.inserted.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"markup.changed.diff, punctuation.definition.changed.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"punctuation.definition.from-file.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"entity.name.section.group-title.ini, punctuation.definition.entity.ini\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f85552\\\"}},{\\\"scope\\\":\\\"punctuation.separator.key-value.ini\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"string.quoted.double.ini, string.quoted.single.ini, punctuation.definition.string.begin.ini, punctuation.definition.string.end.ini\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"keyword.other.definition.ini\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"support.function.aggregate.sql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"string.quoted.single.sql, punctuation.definition.string.end.sql, punctuation.definition.string.begin.sql, string.quoted.double.sql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"support.type.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#dfa000\\\"}},{\\\"scope\\\":\\\"variable.parameter.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"constant.character.enum.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"punctuation.support.type.property-name.begin.json, punctuation.support.type.property-name.end.json, punctuation.separator.dictionary.key-value.json, punctuation.definition.string.begin.json, punctuation.definition.string.end.json, punctuation.separator.dictionary.pair.json, punctuation.separator.array.json\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"support.type.property-name.json\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"string.quoted.double.json\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"punctuation.separator.key-value.mapping.yaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#939f91\\\"}},{\\\"scope\\\":\\\"string.unquoted.plain.out.yaml, string.quoted.single.yaml, string.quoted.double.yaml, punctuation.definition.string.begin.yaml, punctuation.definition.string.end.yaml, string.unquoted.plain.in.yaml, string.unquoted.block.yaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"punctuation.definition.anchor.yaml, punctuation.definition.block.sequence.item.yaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#35a77c\\\"}},{\\\"scope\\\":\\\"keyword.key.toml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f57d26\\\"}},{\\\"scope\\\":\\\"string.quoted.single.basic.line.toml, string.quoted.single.literal.line.toml, punctuation.definition.keyValuePair.toml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#8da101\\\"}},{\\\"scope\\\":\\\"constant.other.boolean.toml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#3a94c5\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.table.toml, punctuation.definition.table.toml, entity.other.attribute-name.table.array.toml, punctuation.definition.table.array.toml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#df69ba\\\"}},{\\\"scope\\\":\\\"comment, string.comment, punctuation.definition.comment\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#939f91\\\"}}],\\\"type\\\":\\\"light\\\"}\"))\n"], "names": ["everforestLight"], "mappings": "AACA,MAAeA,EAAA,OAAO,OAAO,KAAK,MAAM,07oDAA0jyD,CAAC", "x_google_ignoreList": [0]}